import os
import instructor
from openai import OpenAI

os.environ['OPENAI_API_KEY'] = "********************************************************************************************************************************************************************"
# model = "o1-pro-2025-03-19"
model = "gpt-4o-2024-08-06"
client = OpenAI()


client = instructor.patch(client)

from pydantic import BaseModel, Field
from typing import List

class SymptomDetail(BaseModel):
    symptom_name: str = Field(description="Name of the symptom")
    symptom_explanation: str = Field(description="Explanation about the symptom")
    onset: str = Field(description="When did the symptom start")
    severity: str = Field(description="Severity of the symptom: Mild/Moderate/Severe")
    nature: str = Field(description="Nature of the symptom (e.g., sharp pain, dull ache, intermittent, constant)")
    timing: str = Field(description="Specific timing or pattern (e.g., worse in morning, episodic, persistent)")
    
class DifferentialDiagnosis(BaseModel):
    diagnosis: str = Field(description="Name of the probable diagnosis")
    explanation: str = Field(description="Explanation connecting the patient's inputs to this diagnosis")

class TestRecommendation(BaseModel):
    test_name: str = Field(description="Name of the recommended test")
    rationale: str = Field(description="One-liner explanation why the test is recommended")

class ReportVarType(BaseModel):
    patient_details: List[str] = Field(description="Overview of the patient in bulleted points")
    symptoms: List[SymptomDetail] = Field(description="Detailed symptom information")
    medication_history: List[str] = Field(description="Past medications, if none return ['']")
    possible_differentials: List[DifferentialDiagnosis] = Field(description="Top 3 differential diagnoses with explanation")
    relevant_medical_history: List[str] = Field(description="Important past medical, hereditary, allergy, and social history")
    recommended_tests: List[TestRecommendation] = Field(description="Diagnostic tests with explanations")
    next_steps: str = Field(description="Recommended next actions")
    severity_assessment: str = Field(description="Clinical severity assessment (Low/Medium/High)")

def generate_report(chat, Possible_differentials):
    ext = chat
    ext += f' The Medical bot came up with the following differentials {Possible_differentials}'

    prompt = """
You are a highly skilled clinical AI assistant tasked with generating a detailed and clinically relevant patient report in **JSON format** based on the patient's conversation and suspected differentials.

- **Response Format:** Strictly return a JSON object with the following structure:

  - `"patient_details"`: List of short bullet points summarizing the patient (Name, Age, Sex, Diabetic: Yes/No, Obese: Yes/No, etc.)

  - `"symptoms"`: A list of symptom objects. Each symptom object should have:
    - `"symptom_name"`: Name of the symptom (e.g., Chest Pain)
    - `"symptom_explanation"`: Clinical explanation summarizing the symptom
    - `"onset"`: When the symptom started (e.g., 2 days ago, 1 month)
    - `"severity"`: Severity level (Mild/Moderate/Severe)
    - `"nature"`: Nature of the symptom (sharp, dull, constant, intermittent, etc.)
    - `"timing"`: Timing characteristics (worse in morning, episodic, etc.)

  - `"medication_history"`: List of any past or current medications (if none, return [""]).

  - `"possible_differentials"`: List of maximum 3 most probable diagnoses. Each diagnosis must have:
    - `"diagnosis"`: Name of the diagnosis
    - `"explanation"`: Brief explanation linking patient's findings to this diagnosis

  - `"relevant_medical_history"`: Important previous history including:
    - Known drug allergies
    - Food/environmental allergies
    - Relevant hereditary conditions (like diabetes, heart disease, cancer)
    - Social history (smoking, alcohol use, drug use, occupation, recent travel)

  - `"recommended_tests"`: List of diagnostic tests where each test has:
    - `"test_name"`: Name of the recommended test
    - `"rationale"`: One-line explanation why this test is appropriate

  - `"next_steps"`: Clear next action plan or recommendations for management.

  - `"severity_assessment"`: Overall clinical severity assessment as either Low, Medium, or High.

- **Important Instructions:**
  - Be concise yet clinically informative.
  - If any information is missing, use empty strings ("") or [""] appropriately.
  - Prioritize the most clinically relevant information for a physician.
  - **Return only valid JSON. Do not include any extra text, markdown, or explanation outside the JSON.**
"""

    system_prompt = {"role": "system", "content": prompt}
    chat_history = [system_prompt]
    chat_history.append({"role": "user", "content": ext})

    response = client.chat.completions.create(
        model=model,
        messages=chat_history,
        response_model=ReportVarType  # Changed from Report_var_type to ReportVarType
    )
    return response

chat = "{'Name': 'Jk', 'Age': '28', 'Sex': 'Male', 'Smoker': 'Yes', 'Diabetic': 'No', 'Overweight': 'No', 'Phone': '9301817403', 'CountryCode': '91', 'Doctor': 'Lajpat Agarwal', 'AppointmentTime': '2025-05-07T04:30:00.379Z', 'doc_id': '<EMAIL>'}, What brings you in today? : Headache, When did your headache start? : 1-3 days ago, How would you describe the severity of your headache? : Severe, How long does each headache episode last? : More than 3 hours, Do you have any known allergies? : No, Have you experienced any other symptoms along with the headache? : Nausea or vomiting, Where is your headache located? : One side of the head, What triggers your headache? : Stress, Do you experience any aura or visual disturbances before the headache? : Yes, Do you have nasal congestion or facial pain? : Yes"
Possible_differentials = "Migraine, Tension-type headache, Cluster headache, Sinusitis, Medication overuse headache"
generate_report(chat, Possible_differentials)

from dotenv import load_dotenv
from elevenlabs.client import ElevenLabs
from elevenlabs import play

load_dotenv()

elevenlabs = ElevenLabs(
  api_key='***************************************************',
)

audio = elevenlabs.text_to_speech.convert(
    text="The first move is what sets everything in motion.",
    voice_id="JBFqnCBsd6RMkjVDRZzb",
    model_id="eleven_multilingual_v2",
    output_format="mp3_44100_128",
)

play(audio)


