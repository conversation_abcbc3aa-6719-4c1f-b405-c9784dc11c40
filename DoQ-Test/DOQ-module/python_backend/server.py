from fastapi import <PERSON><PERSON><PERSON>, Request, File, UploadFile, Form, BackgroundTasks
from fastapi.responses import JSONResponse, Response
from starlette.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from chatbots import translation, MetaData_agent, Doc_agent, Differentials_agent, regenerate_differentials, generate_report, edit_report, Differentials_ranking_agent, generate_medical_summary, generate_discharge_summary_from_documents
from voice_agents import Voice_MetaData_agent, Voice_Doc_agent
from pydantic import BaseModel, Extra
from openai import OpenAI
from deepgram import DeepgramClient, PrerecordedOptions, FileSource
# Import for speech recognition - will be updated to use a different service
import os
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from typing import Dict, List, Optional
import json
import uuid
import requests
from datetime import datetime
from whatsapp import send_ques, send_message, update_chat_string, send_language_selection, account_sid, auth_token
from voice import update_chat_string_voice, generate_audio, continue_call_with_question, initiate_multilingual_call, generate_bilingual_prompt
from twilio.twiml.voice_response import VoiceR<PERSON>ponse, <PERSON>ather
from firebase import save_new_patient_report, save_doctor_feedback
import redis
from datetime import datetime, timedelta
import time
import asyncio
import boto3
import tempfile
from io import BytesIO
import base64
from pdf2image import convert_from_path
from helper_func import handle_lab_reports

# Import our custom logger
from logger import logger, setup_logging


# from chatbots import

r = redis.Redis(
    host='redis-10760.crce179.ap-south-1-1.ec2.redns.redis-cloud.com',
    port=10760,
    decode_responses=True,
    username="default",
    password="i3JNc2YMkQY6fHMUkXZJ9QfcSAC5qxVF",
)
app = FastAPI()

# Mount the audio_files directory as a static files directory
os.makedirs("audio_files", exist_ok=True)
app.mount("/audio_files", StaticFiles(directory="audio_files"), name="audio_files")

# Create WhatsApp audio directory
os.makedirs("whatsapp_audio", exist_ok=True)
origins = [
    "http://localhost",  # Covers cases where port is omitted
    "https://doq-module.netlify.app",
    "https://doqdevline.netlify.app",
    "https://c924-20-197-54-228.ngrok-free.app"  # Add your ngrok URL here,
    "https://doq-backend.onrender.com"
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins, but filter manually
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

@app.middleware("http")
async def add_cors_headers(request: Request, call_next):
    response = await call_next(request)
    origin = request.headers.get("origin")
    if origin and (origin.startswith("http://localhost") or origin in origins):
        response.headers["Access-Control-Allow-Origin"] = origin
    return response

# Speech recognition API key and client will be updated

DEEPGRAM_API_KEY = "****************************************"
deepgram = DeepgramClient(DEEPGRAM_API_KEY)

# Initialize OpenAI client
client = OpenAI()
os.environ['OPENAI_API_KEY'] = "********************************************************************************************************************************************************************"  # Replace with your actual OpenAI API key


class Template(BaseModel):
    class Config:
        extra = Extra.allow


@app.get("/hello")
def read_root():
    return {"Hello": "World"}



@app.post("/meta-agent")
async def receive_data(data: Template):
    print("Received data ----------\n", data)
    response = MetaData_agent(str(data))
    print('Pushed data ----------\n', response)

    return {
        "question": response.question,
        "choices": response.choices,
        "question_type": response.question_type,
        "end_convo": response.end_convo,
    }

@app.post("/doc-agent")
async def receive_data(data: Template):
    print("Received data for Doc ----------\n")
    # print(str(data.chatHistory['chatHistory']), str(data.differentials['differentials']))
    print(data)
    response = Doc_agent(str(data.chatHistory['chatHistory']), str(data.differentials['differentials']))
    print('Pushed data for Doc ----------\n', response)

    return {
        "question": response.question,
        "choices": response.choices,
        "question_type": response.question_type,
        "end_convo": response.end_convo,
    }


@app.post('/differentials_ranking')
async def receive_data(data: Template):
    print("Received data for Differentials ----------\n", str(data.chatHistory))
    response = Differentials_ranking_agent(str(data.chatHistory))
    print('Pushed data for Differentials ----------\n', response)

    return {
        "differentials": response.differentials,
        "ranking": response.ranking,
    }

@app.post('/report')
async def receive_data(data: Template):
    print("Received data for report ----------\n", data)
    response = generate_report(str(data.chatHistory['chatHistory']), str(data.differentials['differentials']))
    print('Pushed data for report ----------\n', response)

    return {
        "report": response,
    }

@app.post('/doctor-feedback')
async def save_doctor_feedback_endpoint(data: Template):
    try:
        print("Received doctor feedback data ----------\n", data)

        # Extract data from the structured payload
        feedback = data.feedback
        timings = getattr(data, 'timings', {})

        # Get required fields from feedback
        doc_id = feedback.get('doc_id')
        patient_id = feedback.get('patient_id')
        report_key = feedback.get('report_key')

        if not doc_id or not patient_id or not report_key:
            error_msg = f"Missing required fields: doc_id={doc_id}, patient_id={patient_id}, report_key={report_key}"
            print(f"Error saving doctor feedback: {error_msg}")
            return {"status": "error", "error": error_msg}

        # Create feedback data object
        feedback_data = {
            "satisfaction": feedback.get('satisfaction'),
            "timeSaving": feedback.get('timeSaved') or feedback.get('timeSaving'),
            "futureUse": feedback.get('wouldRecommend') or feedback.get('futureUse'),
            "remarks": feedback.get('suggestedImprovements') or feedback.get('remarks', ''),
            "timestamp": feedback.get('timestamp'),
            # Include other feedback fields
            "reportAccuracy": feedback.get('reportAccuracy'),
            "diagnosisSatisfaction": feedback.get('diagnosisSatisfaction'),
            "clinicalValueRating": feedback.get('clinicalValueRating'),
            "missingInfo": feedback.get('missingInfo'),
            # Include timing data
            "timings": timings
        }

        # Add audio filename if available
        if hasattr(timings, 'audioFileName') or (isinstance(timings, dict) and 'audioFileName' in timings):
            audio_filename = timings.audioFileName if hasattr(timings, 'audioFileName') else timings.get('audioFileName')
            if audio_filename:
                feedback_data["audioFileName"] = audio_filename

        response = save_doctor_feedback(doc_id, patient_id, report_key, feedback_data)
        print('Doctor feedback saved ----------\n', response)

        return response
    except Exception as e:
        print(f"Error saving doctor feedback: {str(e)}")
        return {"status": "error", "error": str(e)}

# @app.post('/get_info')
# async def receive_data(data: Template):
#     print("Received data for get_info ----------\n", (data))
#     response = get_info_agent(str(data))
#     return {
#         "response": response,
#     }

@app.post('/report_edit')
async def receive_data(data: Template):
    print("Received data for edit report ----------\n", str(data))
    response = edit_report(str(data.pastReport['pastReport']), str(data.pastReport['commands']))
    print('Pushed data for dit report  ----------\n', response)

    return {
        "report": response,
    }

# @app.post("/upload_audio")
# async def worker_audio(file: UploadFile = File(...), lang: str = Form(None), summarize: bool = Form(True)):
#     try:
#         print(lang)
#         print(summarize)
#         temp_filename = f"temp_{file.filename}"
#         with open(temp_filename, "wb") as buffer:
#             buffer.write(await file.read())
#         # Use a simple speech recognition library like SpeechRecognition
#         # For now, we'll just return a placeholder message
#         transcript = "Speech recognition is currently using Twilio's built-in capabilities."
#         print("transcript: ", transcript)
#         print(type(transcript))

#         if summarize:
#             notes = generate_medical_summary(transcript)
#             print(notes)
#         else:
#             notes = ""

#         os.remove(temp_filename)

#         return {"status": "success", "transcript": transcript, "notes": notes}

#     except Exception as e:
#         return {"status": "error", "error": str(e)}

# @app.post("/upload_audio")
# async def worker_audio(
#     file: UploadFile = File(...),
#     lang: str = Form(None),
#     summarize: bool = Form(True),
#     filename: str = Form(None)
# ):
#     try:
#         print(f"Processing audio upload: lang={lang}, summarize={summarize}, filename={filename}")

#         # Create the audio directory if it doesn't exist
#         AUDIO_DIR = "/Data/conda_envs/Audio_Data"
#         os.makedirs(AUDIO_DIR, exist_ok=True)

#         # Use the provided filename if available, otherwise generate a unique one
#         if filename:
#             # Ensure the filename is safe
#             safe_filename = "".join(c if c.isalnum() or c in "._- " else "_" for c in filename)
#             file_path = os.path.join(AUDIO_DIR, safe_filename)
#         else:
#             # Generate a unique filename with timestamp (UTC+5:30)
#             from datetime import datetime, timedelta
#             ist_time = datetime.utcnow() + timedelta(hours=5, minutes=30)
#             timestamp = ist_time.strftime("%Y%m%d_%H%M%S")
#             safe_filename = f"audio_{timestamp}.webm"
#             file_path = os.path.join(AUDIO_DIR, safe_filename)

#         print(f"Saving audio file to: {file_path}")

#         # Save the uploaded file to the specified path
#         file_content = await file.read()
#         with open(file_path, "wb") as buffer:
#             buffer.write(file_content)

#         # Create a temporary file for processing
#         temp_filename = f"temp_{os.path.basename(file_path)}"
#         with open(temp_filename, "wb") as buffer:
#             buffer.write(file_content)

#         with open(temp_filename, "rb") as audio_file:
#             buffer_data = audio_file.read()

#         payload: FileSource = {
#             "buffer": buffer_data,
#         }

#         if lang == "en":
#             options = PrerecordedOptions(
#                 model="nova-3",
#                 language="en",
#             )
#         else:
#             options = PrerecordedOptions(
#                 model="nova-2",
#                 language="hi"
#             )

#         try:
#             response = deepgram.listen.rest.v("1").transcribe_file(payload, options)
#             transcript = response.results.channels[0].alternatives[0].transcript
#             print("transcript: ", transcript)
#         except Exception as transcription_error:
#             print(f"Error during transcription: {transcription_error}")
#             transcript = ""  # Set empty transcript if transcription fails

#         # Check if transcript is empty
#         if not transcript or transcript.strip() == "":
#             print("Empty transcript detected")
#             if summarize:
#                 # Create a default empty summary with lists
#                 notes = {
#                     "chief_complaint": ["No information available"],
#                     "history_of_present_illness": ["No information available"],
#                     "past_medical_history": ["No information available"],
#                     "medications": ["No information available"],
#                     "vitals_and_examination": ["No information available"],
#                     "assessment": ["No information available"],
#                     "plan": ["No information available"],
#                     "investigations": ["No information available"],
#                     "follow_up_instructions": ["No information available"]
#                 }
#             else:
#                 notes = ""
#         else:
#             # Process non-empty transcript
#             if summarize:
#                 notes = generate_medical_summary(transcript)
#                 print(notes)
#             else:
#                 notes = ""

#         # Clean up the temporary file
#         os.remove(temp_filename)

#         return {
#             "status": "success",
#             "transcript": transcript,
#             "notes": notes,
#             "file_path": file_path,
#             "filename": safe_filename
#         }

#     except Exception as e:
#         print(f"Error processing audio: {str(e)}")
#         return {"status": "error", "error": f"Error processing audio: {str(e)}"}



@app.post("/upload_audio")
async def worker_audio(
    file: UploadFile = File(...),
    lang: str = Form(None),
    summarize: bool = Form(True),
    filename: str = Form(None)
):
    try:
        print(f"Processing audio upload: lang={lang}, summarize={summarize}, filename={filename}")

        # Create the audio directory if it doesn't exist
        AUDIO_DIR = "/Data/conda_envs/Audio_Data"
        os.makedirs(AUDIO_DIR, exist_ok=True)

        # Use the provided filename if available, otherwise generate a unique one
        if filename:
            # Ensure the filename is safe
            safe_filename = "".join(c if c.isalnum() or c in "._- " else "_" for c in filename)
            file_path = os.path.join(AUDIO_DIR, safe_filename)
        else:
            # Generate a unique filename with timestamp (UTC+5:30)
            from datetime import datetime, timedelta
            ist_time = datetime.utcnow() + timedelta(hours=5, minutes=30)
            timestamp = ist_time.strftime("%Y%m%d_%H%M%S")
            safe_filename = f"audio_{timestamp}.webm"
            file_path = os.path.join(AUDIO_DIR, safe_filename)

        print(f"Saving audio file to: {file_path}")

        # Save the uploaded file to the specified path
        file_content = await file.read()

        # Ensure the file content is not empty
        if not file_content:
            raise ValueError("Uploaded file is empty")

        # Save the file with proper binary mode
        with open(file_path, "wb") as buffer:
            buffer.write(file_content)
            buffer.flush()  # Ensure all data is written to disk
            os.fsync(buffer.fileno())  # Force system to write to disk

        # Verify the file exists and has content
        if not os.path.exists(file_path):
            raise ValueError("File was not saved properly")

        file_size = os.path.getsize(file_path)
        if file_size == 0:
            raise ValueError("Saved file is empty")

        print(f"File saved successfully. Size: {file_size} bytes")

        # Convert webm to mp3 using ffmpeg
        mp3_path = file_path.replace('.webm', '.mp3')
        try:
            import subprocess
            # Convert webm to mp3
            subprocess.run([
                'ffmpeg', '-i', file_path,
                '-vn',  # No video
                '-acodec', 'libmp3lame',  # Use MP3 codec
                '-ar', '44100',  # Sample rate
                '-ac', '2',  # Stereo
                '-b:a', '192k',  # Bitrate
                mp3_path
            ], check=True)
            print(f"Converted audio to MP3: {mp3_path}")
        except subprocess.CalledProcessError as e:
            print(f"Error converting audio: {e}")
            raise ValueError("Failed to convert audio format")

        try:
            # Use OpenAI's transcription service with gpt-4o-transcribe
            with open(mp3_path, "rb") as audio_file:
                # Read the file content to verify it's readable
                audio_content = audio_file.read()
                if not audio_content:
                    raise ValueError("Could not read audio file content")

                # Reset file pointer to beginning
                audio_file.seek(0)

                # Start timing transcription
                import time
                transcription_start = time.time()

                transcription = client.audio.transcriptions.create(
                    model="gpt-4o-mini-transcribe",
                    file=audio_file,
                    prompt="Transcribe only what is actually spoken in the audio. Do not generate fillers or repeated words. Stop transcription at end of speech.",
                    temperature=0.3
                )

                transcription_end = time.time()
                transcription_time = transcription_end - transcription_start
                print(f"Transcription completed in {transcription_time:.2f} seconds")

            transcript = transcription.text
            print("Transcript:", transcript)

            # Step 2: Translate the transcript to fluent English using GPT
            translation_start = time.time()

            translated_text= translation(transcript)
            translation_end = time.time()
            translation_time = translation_end - translation_start
            print(f"Translation completed in {translation_time:.2f} seconds")

            # translated_text = translation.choices[0].message.content
            print("Translated to English:", translated_text)
            transcript = translated_text

        except Exception as transcription_error:
            print(f"Error during transcription: {transcription_error}")
            # Print more detailed error information
            if hasattr(transcription_error, 'response'):
                print(f"Response status: {transcription_error.response.status_code}")
                print(f"Response body: {transcription_error.response.text}")
            transcript = ""  # Set empty transcript if transcription fails

        # Check if transcript is empty
        if not transcript or transcript.strip() == "":
            print("Empty transcript detected")
            if summarize:
                # Create a default empty summary with lists
                notes = {
                    "chief_complaint": ["No information available"],
                    "history_of_present_illness": ["No information available"],
                    "past_medical_history": ["No information available"],
                    "medications": ["No information available"],
                    "vitals_and_examination": ["No information available"],
                    "assessment": ["No information available"],
                    "plan": ["No information available"],
                    "investigations": ["No information available"],
                    "follow_up_instructions": ["No information available"]
                }
            else:
                notes = ""
        else:
            # Process non-empty transcript
            if summarize:
                notes = generate_medical_summary(transcript)
                print(notes)
            else:
                notes = ""

        # Clean up the temporary file
        # os.remove(temp_filename)

        return {
            "status": "success",
            "transcript": transcript,
            "notes": notes,
            "file_path": file_path,
            "filename": safe_filename
        }

    except Exception as e:
        print(f"Error processing audio: {str(e)}")
        return {"status": "error", "error": f"Error processing audio: {str(e)}"}




# Sessions for mobile-web communication
sessions: Dict[str, Dict[str, WebSocket]] = {}

# IPD WebSocket connections
ipd_connections: Dict[str, List[WebSocket]] = {}
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    session_id = None
    role = None
    try:
        # Receive initial message as text and parse it
        init_data_raw = await websocket.receive_text()
        try:
            init_data = json.loads(init_data_raw)
            session_id = init_data["session_id"]
            role = init_data["role"]
        except json.JSONDecodeError:
            print(f"⚠️ Received invalid initial JSON: {init_data_raw}")
            return  # Close connection if initial message isn't valid JSON

        if session_id not in sessions:
            sessions[session_id] = {}

        sessions[session_id][role] = websocket
        print(f"🔗 {role} joined session {session_id}")
        print(f"Current sessions: {sessions.keys()}, Roles for {session_id}: {sessions[session_id].keys()}")
        # Send status update to web if connected
        if role == "mobile" and "web" in sessions[session_id]:
            status_update = json.dumps({"status": "mobile_connected"})
            await sessions[session_id]["web"].send_text(status_update)
            print(f"📤 Sent status update to web: {status_update}")

        elif role == "web" and "mobile" not in sessions[session_id]:
            status_update = json.dumps({"status": "mobile_not_connected"})
            await websocket.send_text(status_update)
            print(f"📤 Informed web that mobile is not connected: {status_update}")

        while True:
            try:
                # Receive data (text or bytes)
                data = await websocket.receive()

                # Handle binary data (audio) from mobile
                if role == "mobile" and "bytes" in data:
                    audio_data = data["bytes"]
                    # async with aiofiles.open(f'recording_{session_id}.wav', "wb") as audio_file:
                    #     await audio_file.write(audio_data)
                    # print(f"💾 Saved {len(audio_data)} bytes to recording_{session_id}.wav")
                    if "web" in sessions[session_id]:
                        await sessions[session_id]["web"].send_bytes(audio_data)
                        print(f"📤 Sent {len(audio_data)} bytes to web client")

                # Handle text data (status from mobile or commands from web)
                elif "text" in data:
                    text_data = data["text"]
                    try:
                        text_json = json.loads(text_data)

                        if role == "mobile" and "status" in text_json:
                            # Forward status updates to web
                            if "web" in sessions[session_id]:
                                await sessions[session_id]["web"].send_text(text_data)
                                print(f"📤 Forwarded status to web: {text_data}")

                        elif role == "web" and "command" in text_json:
                            # Forward commands to mobile
                            if "mobile" in sessions[session_id]:
                                await sessions[session_id]["mobile"].send_text(text_data)
                                print(f"📤 Sent command to mobile: {text_data}")
                            else:
                                print(f"📥 Received from web (no mobile connected): {text_data}")
                        else:
                            print(f"📥 Received valid JSON from {role}: {text_data}")
                    except json.JSONDecodeError:
                        # Handle non-JSON text (e.g., "ping")
                        print(f"📥 Received non-JSON text from {role}: {text_data}")

            except WebSocketDisconnect:
                print(f"❌ {role} disconnected from session {session_id}")
                break

    except WebSocketDisconnect as e:
        print(f"❌ {role} disconnected from session {session_id} (reason: {e})")
    except Exception as e:
        print(f"⚠️ Unexpected error for {role} in session {session_id}: {e}")
    finally:
        if session_id and role and session_id in sessions and role in sessions[session_id]:
            del sessions[session_id][role]
            print(f"🗑️ Removed {role} from session {session_id}")

            # Send status update to web if mobile disconnects
            if role == "mobile" and "web" in sessions[session_id]:
                status_update = json.dumps({"status": "mobile_disconnected"})
                await sessions[session_id]["web"].send_text(status_update)
                print(f"📤 Sent status update to web: {status_update}")
            if not sessions[session_id]:
                del sessions[session_id]
                print(f"🗑️ Session {session_id} deleted")


# PatientNumber is whatsapp:+{countrycode}{phone}
def format_date_for_firebase():
    # Add 5 hours and 30 minutes to the current time for UTC+5:30
    now = datetime.now() + timedelta(hours=5, minutes=30)
    return str(now.strftime("%d_%b_%Y_%H_%M_%S"))

@app.post("/ping_patient_pre_charting")
async def worker_ping_patient_pre_charting(data: Template):

    try:
        patient_id = data.patientId
        doc_id = data.doc_id
        Patientdata = data.patientData

        # Check if voice mode is requested
        use_voice = getattr(data, 'useVoice', False)

        if use_voice:
            # For voice calls, we don't use the whatsapp: prefix
            PATIENT_NUMBER = f"+{Patientdata['CountryCode']}{Patientdata['Phone']}"
            # We'll store the session with a voice: prefix to distinguish from WhatsApp
            redis_key = f"session:voice:{PATIENT_NUMBER}"
        else:
            # For WhatsApp, use the existing flow
            PATIENT_NUMBER = f"whatsapp:+{Patientdata['CountryCode']}{Patientdata['Phone']}"
            redis_key = f"session:{PATIENT_NUMBER}"
            # Send language selection message instead of appointment reminder
            send_language_selection(PATIENT_NUMBER)

        session_data = {
            "doc_id": doc_id,
            "Patientdata": Patientdata,
            "patient_id": patient_id,
            "report_key": format_date_for_firebase(),
            "chat_history": {'question': [], 'choice': [], 'question_type': [], 'end_convo': False},
            "convo_state": "language_select",  # Start with language selection
            'chat_string': '',
            'differentials': '',
            'use_voice': use_voice,
            'language': 'en'  # Default language is English
        }

        r.set(redis_key, json.dumps(session_data), ex=1800)

        # If using voice, initiate the first call immediately
        if use_voice:
            # We'll initiate the first call when the patient responds to the appointment reminder
            # This will be handled by the voice-start endpoint
            pass

        return {"status": "success"}

    except Exception as e:
        print(e)
        return {"status": "error", "error": str(e)}


@app.post("/twilio-whatsapp-webhook")
async def handle_twilio_webhook(request: Request):
    try:    
        form = await request.form()
        print(form)
        from_number = form.get("From")
        message_body = form.get("Body", "")  # Default to empty string if no text
        conversation_sid = form.get("ConversationSid")

        # Check for media attachments (audio, image, PDF)
        num_media = int(form.get("NumMedia", "0"))
        media_url = None
        media_content_type = None

        # If there's any media, get the URL and content type
        if num_media > 0:
            media_content_type = form.get("MediaContentType0", "")
            media_url = form.get("MediaUrl0")

            # Log the media type for debugging
            if media_content_type.startswith("audio/"):
                print(f"[Incoming WhatsApp Reply] Audio detected: {media_url}, Content-Type: {media_content_type}")
            elif media_content_type.startswith("image/"):
                print(f"[Incoming WhatsApp Reply] Image detected: {media_url}, Content-Type: {media_content_type}")
            elif media_content_type == "application/pdf":
                print(f"[Incoming WhatsApp Reply] PDF detected: {media_url}, Content-Type: {media_content_type}")
            else:
                print(f"[Incoming WhatsApp Reply] Media detected: {media_url}, Content-Type: {media_content_type}")

        print(f"[Incoming WhatsApp Reply] From: {from_number}, Body: {message_body}, Media: {media_url}, Media Type: {media_content_type}, Conversation SID: {conversation_sid}")

        # Print all form data for debugging
        print("[Incoming WhatsApp Reply] All form data:")
        for key, value in form.items():
            print(f"  {key}: {value}")
        redis_key = f"session:{from_number}"
        session_json = r.get(redis_key)
        session = json.loads(session_json)

        # Check if the user wants to restart the interview
        if message_body.lower() == "start":
            # Get the selected language from the session (default to English if not set)
            language = session.get('language', 'en')

            # Reset the chat history
            access_data = session["Patientdata"]
            response = MetaData_agent(str(access_data))
            send_ques(from_number, response.question, response.choices, response.question_type, language)
            session['chat_history']['question'] = [response.question]
            session['chat_history']['choice'] = [response.choices]
            session['chat_history']['question_type'] = [response.question_type]
            session['chat_history']['end_convo'] = response.end_convo
            session['chat_string'] = f'{access_data}'
            session['convo_state'] = "meta"
            r.set(redis_key, json.dumps(session), ex=1800)
            return

        # Handle lab reports state
        if session["convo_state"] == "lab_reports":
            handle_lab_reports(session, message_body, media_url, from_number, auth_token, account_sid, r, redis_key)
            return

        # Handle language selection
        if session["convo_state"] == "language_select":
            session = update_chat_string(session, message_body, from_number, media_url)
            r.set(redis_key, json.dumps(session), ex=1800)
            return

        elif session["convo_state"] == "meta":
            # Get the selected language from the session (default to English if not set)
            language = session.get('language', 'en')

            session = update_chat_string(session, message_body, from_number, media_url)

            if len(session['chat_history']['question']) > 7 or session['chat_history']['end_convo']:
                print('meta end')

                session['convo_state'] = "doc"
                differentials = Differentials_agent(str(session['chat_string']))
                session['differentials'] = differentials

                response = Doc_agent(str(session['chat_string']), differentials)

                send_ques(from_number, response.question, response.choices, response.question_type, language)
                session['chat_history']['question'].append(response.question)
                session['chat_history']['choice'].append(response.choices)
                session['chat_history']['question_type'].append(response.question_type)
                r.set(redis_key, json.dumps(session), ex=1800)
                return

            response = MetaData_agent(str(session['chat_string']))
            # print("length: ", len(session['chat_history']['question']))
            # print("Question: ", response.question, "Choices: ", response.choices)
            send_ques(from_number, response.question, response.choices, response.question_type, language)
            session['chat_history']['question'].append(response.question)
            session['chat_history']['choice'].append(response.choices)
            session['chat_history']['question_type'].append(response.question_type)
            session['chat_history']['end_convo'] = response.end_convo
            r.set(redis_key, json.dumps(session), ex=1800)

        elif session["convo_state"] == "doc":
            # Get the selected language from the session (default to English if not set)
            language = session.get('language', 'en')

            session = update_chat_string(session, message_body, from_number, media_url)

            if len(session['chat_history']['question']) > 8 or session['chat_history']['end_convo']:
                print('doc end')
                session['convo_state'] = "end"

                # Regenerate differentials using the complete chat history
                print('Regenerating differentials with complete chat history')
                updated_differentials = regenerate_differentials(str(session['chat_string']))

                # Store both the original and updated differentials
                session['original_differentials'] = session['differentials']
                session['differentials'] = updated_differentials

                # Add the updated differentials to the chat string with a prefix
                session['display_chat_string'] = session['chat_string'] + ", Assessment: " + updated_differentials

                # Send thank you message in the selected language
                if language == "hi":
                    thank_you_message = "आपके जवाबों के लिए धन्यवाद। आपकी प्री-कंसल्टेशन रिपोर्ट डॉक्टर के साथ साझा की गई है।"
                else:
                    thank_you_message = "Thank you for your responses. Your Precounselling report is shared with the doctor."

                send_message(from_number, thank_you_message, language)

                # Add a follow-up message asking for lab reports
                if language == "hi":
                    lab_report_message = "यदि आपके पास कोई लैब रिपोर्ट है जिसे आप साझा करना चाहते हैं, तो कृपया पीडीएफ या इमेज भेजें। जब आप पूरा कर लें, तो 'exit' टाइप करें।"
                else:
                    lab_report_message = "If you have any lab reports that you would like to share, kindly send us the PDF or image. Once done, type 'exit'."

                send_message(from_number, lab_report_message, language)
                
                # Generate the report with the updated differentials
                report = generate_report(str(session['display_chat_string']), updated_differentials)

                report_dict = report.model_dump()

                save_new_patient_report(session['doc_id'], session['patient_id'], session['report_key'], report_dict)

                # Change conversation state to wait for lab reports
                session['convo_state'] = "lab_reports"
                session['lab_reports'] = []  # Initialize empty list to store lab report data
                r.set(redis_key, json.dumps(session), ex=1800)  # Extend session timeout
                return
            else:
                # Continue with the next question in the doc phase
                # print("length: ", len(session['chat_history']['question']))
                # print("Question: ", response.question, "Choices: ", response.choices)
                response = Doc_agent(str(session['chat_string']), session['differentials'])
                send_ques(from_number, response.question, response.choices, response.question_type, language)
                session['chat_history']['question'].append(response.question)
                session['chat_history']['choice'].append(response.choices)
                session['chat_history']['question_type'].append(response.question_type)
                r.set(redis_key, json.dumps(session), ex=1800)
    except Exception as e:
        print(f"Twilio Processing Error: {str(e)}")
        return 


@app.post("/voice-start")
async def start_voice_consultation(request: Request):
    """Endpoint to start a voice consultation with a patient"""
    try:
        # Print the raw request for debugging
        print("[Voice Start] Received request to start voice consultation")

        form = await request.form()
        patient_number = form.get("patient_number")
        base_url = form.get("base_url", "https://c924-20-197-54-228.ngrok-free.app")

        print(f"[Voice Start] Patient number: {patient_number}, Base URL: {base_url}")

        if not patient_number:
            print("[Voice Start] Error: Patient number is required")
            return {"status": "error", "error": "Patient number is required"}

        # Get the session data
        redis_key = f"session:voice:{patient_number}"
        session_json = r.get(redis_key)

        if not session_json:
            print(f"[Voice Start] Error: No session found for {patient_number}")
            return {"status": "error", "error": "No session found for this patient"}

        session = json.loads(session_json)

        # Get the first question from the voice-specific meta agent
        access_data = session["Patientdata"]
        response = Voice_MetaData_agent(str(access_data))

        # Note: The question will be translated later when generating audio

        # Update the session with the first question
        session['chat_history']['question'] = [response.question]
        session['chat_history']['choice'] = [response.choices]
        session['chat_history']['question_type'] = [response.question_type]
        session['chat_history']['end_convo'] = response.end_convo
        session['chat_string'] = f'{access_data}'
        session['convo_state'] = "meta"

        # Save the updated session
        r.set(redis_key, json.dumps(session), ex=1800)

        print(f"[Voice Start] Initiating call to {patient_number} with question: {response.question}")

        # Initiate the call with language selection first
        call = initiate_multilingual_call(
            patient_number,
            response.question,
            response.choices,
            response.question_type,
            base_url
        )

        # Store the first question in the session for use after language selection
        session['first_question'] = response.question

        print(f"[Voice Start] Call initiated, SID: {call.sid}")
    except Exception as e:
        print(f"[Voice Start] Error: {str(e)}")
        return {"status": "error", "error": str(e)}

    return {"status": "success", "call_sid": call.sid}


@app.post("/voice-response")
async def handle_voice_response(request: Request):
    """Webhook for handling voice responses from Twilio"""
    try:
        # Print the raw request for debugging
        print("[Voice Response] Received webhook from Twilio")

        # Get form data
        form = await request.form()

        # Print all form data for debugging
        print("[Voice Response] Form data:")
        for key, value in form.items():
            print(f"  {key}: {value}")

        call_sid = form.get("CallSid")
        from_number = form.get("From")
        to_number = form.get("To")  # This is the patient's number
        speech_result = form.get("SpeechResult")
        digits_pressed = form.get("Digits")  # Check if any digits were pressed

        # Enhanced logging for debugging DTMF input
        print(f"[Voice Response] From: {from_number}, To: {to_number}, Speech: {speech_result}, Digits: {digits_pressed}, Call SID: {call_sid}")
        print(f"[Voice Response] DTMF Debug - Digits pressed: '{digits_pressed}', Type: {type(digits_pressed)}")

        # Get the session data - use the To number (patient's number) instead of From (Twilio number)
        redis_key = f"session:voice:{to_number}"
        session_json = r.get(redis_key)

        if not session_json:
            print(f"[Voice Response] No session found for {to_number}")

            # Try with the From number as a fallback (for backward compatibility)
            redis_key = f"session:voice:{from_number}"
            session_json = r.get(redis_key)

            if not session_json:
                print(f"[Voice Response] No session found for {from_number} either")
            # If no session is found, end the call
            response = VoiceResponse()
            response.say("Sorry, we couldn't find your session. Please try again later.")
            response.hangup()
            return Response(content=str(response), media_type="application/xml")

    except Exception as e:
        print(f"[Voice Response] Error processing request: {str(e)}")
        # Return a TwiML response with an error message
        response = VoiceResponse()
        response.say("Sorry, an error occurred. Please try again later.")
        response.hangup()
        return Response(content=str(response), media_type="application/xml")

    # Parse the session JSON
    session = json.loads(session_json)

    # Check if the user pressed 3 to repeat the question
    if digits_pressed == "3":
        # Create TwiML to repeat the last question
        voice_response = VoiceResponse()

        # Get the selected language from the session (default to English if not set)
        language = session.get('language', 'en')

        # Get the last question
        last_question = session['chat_history']['question'][-1]

        # Use standard messages that will be translated if needed
        repeat_intro_text = "Let me repeat the question."

        repeat_intro_audio = generate_audio(repeat_intro_text, f"repeat_intro_{call_sid}.mp3", language)
        question_audio = generate_audio(last_question, f"repeat_question_{call_sid}.mp3", language)

        # Get the base URL for serving audio files
        base_url = str(request.url).split('/voice-response')[0]

        # Play the intro audio if available, otherwise fall back to Twilio TTS
        if repeat_intro_audio:
            voice_response.play(f"{base_url}/audio_files/{os.path.basename(repeat_intro_audio)}")
        else:
            voice_response.say(repeat_intro_text)

        # Play the question audio if available, otherwise fall back to Twilio TTS
        if question_audio:
            voice_response.play(f"{base_url}/audio_files/{os.path.basename(question_audio)}")
        else:
            voice_response.say(f"{last_question}")

        # Add gather for collecting input - with language-specific speech model and DTMF
        # Make sure to explicitly set numDigits=1 to capture single digit presses immediately
        if language == "hi":
            # For Hindi, use a model that can handle Hindi speech
            gather = Gather(
                input='speech dtmf',
                action='/voice-response',
                method='POST',
                speechTimeout='auto',
                numDigits=1,  # Capture single digit presses immediately
                language="hi-IN"
            )
        else:
            # For English, use the default phone_call model
            gather = Gather(
                input='speech dtmf',
                action='/voice-response',
                method='POST',
                speechTimeout='auto',
                numDigits=1,  # Capture single digit presses immediately
                speechModel='phone_call'
            )
        voice_response.append(gather)

        # If no input is received, redirect to voice-repeat
        voice_response.redirect("/voice-repeat")

        return Response(content=str(voice_response), media_type="application/xml")

    # If not repeating, update the chat history with the transcribed response (only if speech was detected)
    if speech_result:
        session = update_chat_string_voice(session, speech_result)

    # Determine the next question based on the current state
    if session["convo_state"] == "meta":
        if len(session['chat_history']['question']) > 3 or session['chat_history']['end_convo']:
            print('meta end')
            session['convo_state'] = "doc"
            # Generate differentials
            differentials = Differentials_agent(str(session['chat_string']))
            # Store the differentials in the session
            session['differentials'] = differentials
            # Add the differentials to the chat string with a prefix, but only for display purposes
            # We'll use a separate variable to track the chat string with differentials for display
            session['display_chat_string'] = session['chat_string'] + ", Possible differential: " + differentials

            # Use the original chat string (without differentials) when calling Voice_Doc_agent
            # The differentials are passed as a separate parameter
            response_agent = Voice_Doc_agent(str(session['chat_string']), differentials)
        else:
            response_agent = Voice_MetaData_agent(str(session['chat_string']))

    elif session["convo_state"] == "doc":
        if len(session['chat_history']['question']) > 6 or session['chat_history']['end_convo']:
            print('doc end')
            session['convo_state'] = "end"

            # Regenerate differentials using the complete chat history
            print('Regenerating differentials with complete chat history')
            updated_differentials = regenerate_differentials(str(session['chat_string']))

            # Store both the original and updated differentials
            session['original_differentials'] = session['differentials']
            session['differentials'] = updated_differentials

            # Add the updated differentials to the chat string with a prefix
            session['display_chat_string'] = session['chat_string'] + ", Possible differential: " + updated_differentials

            # Generate the report with the updated differentials
            report = generate_report(str(session['display_chat_string']), updated_differentials)

            report_dict = report.model_dump()

            save_new_patient_report(session['doc_id'], session['patient_id'], session['report_key'], report_dict)

            # Create TwiML to thank the patient and end the call
            voice_response = VoiceResponse()

            # Get the selected language from the session (default to English if not set)
            language = session.get('language', 'en')

            # Generate audio for the thank you message with the selected language
            thank_you_text = "Thank you for your responses. Your pre-consultation report has been shared with the doctor. Goodbye."

            thank_you_audio = generate_audio(thank_you_text, f"thank_you_{call_sid}.mp3", language)

            # Get the base URL for serving audio files
            base_url = str(request.url).split('/voice-response')[0]

            # Play the thank you audio if available, otherwise fall back to Twilio TTS
            if thank_you_audio:
                voice_response.play(f"{base_url}/audio_files/{os.path.basename(thank_you_audio)}")
            else:
                voice_response.say(thank_you_text)
            voice_response.hangup()

            # Delete the session
            r.delete(redis_key)

            return Response(content=str(voice_response), media_type="application/xml")
        else:
            # Use the original chat string (without differentials) when calling Voice_Doc_agent
            # The differentials are passed as a separate parameter
            response_agent = Voice_Doc_agent(str(session['chat_string']), session['differentials'])

    # Update the session with the new question
    session['chat_history']['question'].append(response_agent.question)
    session['chat_history']['choice'].append(response_agent.choices)
    session['chat_history']['question_type'].append(response_agent.question_type)
    if hasattr(response_agent, 'end_convo'):
        session['chat_history']['end_convo'] = response_agent.end_convo

    # Save the updated session
    r.set(redis_key, json.dumps(session), ex=1800)

    # Create TwiML for the next question in a conversational manner
    voice_response = VoiceResponse()

    # Get the selected language from the session (default to English if not set)
    language = session.get('language', 'en')

    # Generate audio for the question with the selected language
    # The translation happens inside the generate_audio function if language is Hindi
    question_audio = generate_audio(response_agent.question, f"question_{call_sid}.mp3", language)

    # We no longer need the no-response audio since we're keeping the call open
    # and letting the user press 3 to repeat the question if needed

    # Get the base URL for serving audio files
    base_url = str(request.url).split('/voice-response')[0]

    # Add the question using Deepgram audio if available, otherwise fall back to Twilio TTS
    if question_audio:
        voice_response.play(f"{base_url}/audio_files/{os.path.basename(question_audio)}")
    else:
        voice_response.say(f"{response_agent.question}")

    # Add gather for collecting input - with language-specific speech model and DTMF
    # Make sure to explicitly set numDigits=1 to capture single digit presses immediately
    if language == "hi":
        # For Hindi, use a model that can handle Hindi speech
        gather = Gather(
            input='speech dtmf',
            action='/voice-response',
            method='POST',
            speechTimeout='auto',
            timeout=30,  # Wait up to 30 seconds for input
            numDigits=1,  # Capture single digit presses immediately
            language="hi-IN"
        )
    else:
        # For English, use the default phone_call model
        gather = Gather(
            input='speech dtmf',
            action='/voice-response',
            method='POST',
            speechTimeout='auto',
            timeout=30,  # Wait up to 30 seconds for input
            numDigits=1,  # Capture single digit presses immediately
            speechModel='phone_call'
        )
    voice_response.append(gather)

    # Instead of repeating the question when no response is received,
    # we'll just keep listening by not adding any redirect or additional instructions
    # The user can press 3 to repeat the question if needed

    return Response(content=str(voice_response), media_type="application/xml")


@app.post("/voice-repeat")
async def repeat_voice_question(request: Request):
    """Endpoint to repeat the last question when no response is received"""
    try:
        # Print the raw request for debugging
        print("[Voice Repeat] Received request to repeat question")

        # Get form data
        form = await request.form()
        call_sid = form.get("CallSid")
        to_number = form.get("To")  # This is the patient's number

        print(f"[Voice Repeat] To: {to_number}, Call SID: {call_sid}")

        # Get the session data
        redis_key = f"session:voice:{to_number}"
        session_json = r.get(redis_key)

        if not session_json:
            print(f"[Voice Repeat] No session found for {to_number}")
            # If no session is found, end the call
            response = VoiceResponse()
            response.say("Sorry, we couldn't find your session. Please try again later.")
            response.hangup()
            return Response(content=str(response), media_type="application/xml")

        # Parse the session JSON
        session = json.loads(session_json)

        # Get the last question
        last_question = session['chat_history']['question'][-1]

        # Create TwiML to repeat the question
        voice_response = VoiceResponse()

        # Get the selected language from the session (default to English if not set)
        language = session.get('language', 'en')

        # Use standard messages that will be translated if needed
        repeat_intro_text = "Let me repeat the question."

        repeat_intro_audio = generate_audio(repeat_intro_text, f"repeat_intro_{call_sid}.mp3", language)
        question_audio = generate_audio(last_question, f"repeat_question_{call_sid}.mp3", language)

        # Get the base URL for serving audio files
        base_url = str(request.url).split('/voice-repeat')[0]

        # Play the intro audio if available, otherwise fall back to Twilio TTS
        if repeat_intro_audio:
            voice_response.play(f"{base_url}/audio_files/{os.path.basename(repeat_intro_audio)}")
        else:
            voice_response.say(repeat_intro_text)

        # Play the question audio if available, otherwise fall back to Twilio TTS
        if question_audio:
            voice_response.play(f"{base_url}/audio_files/{os.path.basename(question_audio)}")
        else:
            voice_response.say(f"{last_question}")

        # Add gather for collecting input - with language-specific speech model and DTMF
        # Make sure to explicitly set numDigits=1 to capture single digit presses immediately
        if language == "hi":
            # For Hindi, use a model that can handle Hindi speech
            gather = Gather(
                input='speech dtmf',
                action='/voice-response',
                method='POST',
                speechTimeout='auto',
                timeout=30,  # Wait up to 30 seconds for input
                numDigits=1,  # Capture single digit presses immediately
                language="hi-IN"
            )
        else:
            # For English, use the default phone_call model
            gather = Gather(
                input='speech dtmf',
                action='/voice-response',
                method='POST',
                speechTimeout='auto',
                timeout=30,  # Wait up to 30 seconds for input
                numDigits=1,  # Capture single digit presses immediately
                speechModel='phone_call'
            )
        voice_response.append(gather)

        # Instead of repeating the question when no response is received,
        # we'll just keep listening by not adding any redirect or additional instructions
        # The user can press 3 to repeat the question if needed

        return Response(content=str(voice_response), media_type="application/xml")

    except Exception as e:
        print(f"[Voice Repeat] Error: {str(e)}")
        # Return a TwiML response with an error message
        response = VoiceResponse()
        response.say("Sorry, an error occurred. Please try again later.")
        response.hangup()
        return Response(content=str(response), media_type="application/xml")


@app.post("/voice-language-select")
async def handle_language_selection(request: Request):
    """Webhook for handling language selection from Twilio"""
    try:
        # Print the raw request for debugging
        print("[Voice Language Select] Received webhook from Twilio")

        # Get form data
        form = await request.form()

        # Print all form data for debugging
        print("[Voice Language Select] Form data:")
        for key, value in form.items():
            print(f"  {key}: {value}")

        call_sid = form.get("CallSid")
        to_number = form.get("To")  # This is the patient's number
        digits_pressed = form.get("Digits")  # This will be '1' for English or '2' for Hindi

        print(f"[Voice Language Select] To: {to_number}, Digits: {digits_pressed}, Call SID: {call_sid}")

        # Get the session data
        redis_key = f"session:voice:{to_number}"
        session_json = r.get(redis_key)

        if not session_json:
            print(f"[Voice Language Select] No session found for {to_number}")
            # If no session is found, end the call
            response = VoiceResponse()
            response.say("Sorry, we couldn't find your session. Please try again later.")
            response.hangup()
            return Response(content=str(response), media_type="application/xml")

        # Parse the session JSON
        session = json.loads(session_json)

        # Set the language based on the digits pressed
        if digits_pressed == "1":
            language = "en"  # English
        elif digits_pressed == "2":
            language = "hi"  # Hindi
        else:
            # Default to English if an invalid option was selected
            language = "en"

        # Store the selected language in the session
        session['language'] = language
        r.set(redis_key, json.dumps(session), ex=1800)

        print(f"[Voice Language Select] Selected language: {language}")

        # Get the base URL for the server
        base_url = str(request.url).split('/voice-language-select')[0]

        # Get the first question from the session
        first_question = session.get('first_question', "Whats your primary reason for visiting to doctor")

        # Generate TwiML for continuing the call with the first question
        response = continue_call_with_question(base_url, first_question, language)

        return Response(content=str(response), media_type="application/xml")

    except Exception as e:
        print(f"[Voice Language Select] Error: {str(e)}")
        # Return a TwiML response with an error message
        response = VoiceResponse()
        response.say("Sorry, an error occurred. Please try again later.")
        response.hangup()
        return Response(content=str(response), media_type="application/xml")


@app.post("/voice-repeat-language")
async def repeat_language_selection(request: Request):
    """Endpoint to repeat the language selection when no response is received"""
    try:
        # Print the raw request for debugging
        print("[Voice Repeat Language] Received request to repeat language selection")

        # Get form data
        form = await request.form()
        call_sid = form.get("CallSid")

        # Create TwiML to repeat the language selection
        response = VoiceResponse()

        # Generate bilingual language selection prompt
        lang_select_audio = generate_bilingual_prompt()

        # Get the base URL for the server
        base_url = str(request.url).split('/voice-repeat-language')[0]

        # Play the language selection prompt
        if lang_select_audio:
            response.play(f"{base_url}/audio_files/{os.path.basename(lang_select_audio)}")
        else:
            # Fallback to Twilio TTS if audio generation fails
            response.say("Welcome to DoQ pre-consultation service. For English, press 1. Hindi ke liye, 2 dabayen. You can press 3 anytime to repeat a question. Sawal ko dohrane ke liye, 3 dabayen.")

        # Add gather for collecting language preference using keypad
        gather = Gather(input='dtmf', action=f"{base_url}/voice-language-select", method='POST', numDigits=1, timeout=10)
        response.append(gather)

        # If no input is received again, end the call
        response.say("We still didn't receive your selection. Please call back later.")
        response.hangup()

        return Response(content=str(response), media_type="application/xml")

    except Exception as e:
        print(f"[Voice Repeat Language] Error: {str(e)}")
        # Return a TwiML response with an error message
        response = VoiceResponse()
        response.say("Sorry, an error occurred. Please try again later.")
        response.hangup()
        return Response(content=str(response), media_type="application/xml")


def cleanup_audio_files(call_sid):
    """Delete audio files associated with a specific call_sid"""
    try:
        # Get all files in the audio directory
        audio_dir = "audio_files"
        if not os.path.exists(audio_dir):
            return

        # Find and delete files containing the call_sid in their name
        count = 0
        for filename in os.listdir(audio_dir):
            if call_sid in filename:
                file_path = os.path.join(audio_dir, filename)
                try:
                    os.remove(file_path)
                    count += 1
                    logger.info(f"[Cleanup] Deleted audio file: {filename}")
                except Exception as e:
                    logger.error(f"[Cleanup] Error deleting file {filename}: {str(e)}")

        logger.info(f"[Cleanup] Removed {count} audio files for call {call_sid}")
    except Exception as e:
        logger.error(f"[Cleanup] Error during cleanup: {str(e)}")


def cleanup_old_audio_files(max_age_hours=24):
    """Delete audio files older than the specified age in hours"""
    try:
        # Get all files in the audio directory
        audio_dir = "audio_files"
        if not os.path.exists(audio_dir):
            return

        # Get current time
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600

        # Find and delete old files
        count = 0
        for filename in os.listdir(audio_dir):
            # Skip lang_select_combined.mp3 as it's reused
            if filename == "lang_select_combined.mp3":
                continue

            file_path = os.path.join(audio_dir, filename)
            try:
                # Get file modification time
                file_mod_time = os.path.getmtime(file_path)
                file_age_seconds = current_time - file_mod_time

                # Delete if older than max age
                if file_age_seconds > max_age_seconds:
                    os.remove(file_path)
                    count += 1
                    logger.info(f"[Cleanup] Deleted old audio file: {filename} (age: {file_age_seconds/3600:.1f} hours)")
            except Exception as e:
                logger.error(f"[Cleanup] Error processing file {filename}: {str(e)}")

        if count > 0:
            logger.info(f"[Cleanup] Removed {count} old audio files")
    except Exception as e:
        logger.error(f"[Cleanup] Error during old file cleanup: {str(e)}")


@app.post("/voice-status")
async def handle_voice_status(request: Request):
    """Webhook for handling voice call status updates from Twilio"""
    form = await request.form()
    call_sid = form.get("CallSid")
    call_status = form.get("CallStatus")

    print(f"[Voice Status] Call SID: {call_sid}, Status: {call_status}")

    # Clean up audio files when call is finished
    if call_status in ["completed", "failed", "busy", "no-answer", "canceled"]:
        # Add a small delay to ensure all files are created before cleanup
        # This is needed because the status webhook might be called before
        # all audio processing is complete
        await asyncio.sleep(2)
        cleanup_audio_files(call_sid)

    return {"status": "success"}


@app.post("/cleanup-audio")
async def manual_cleanup_audio(max_age_hours: int = 24):
    """Manually trigger cleanup of old audio files"""
    try:
        cleanup_old_audio_files(max_age_hours)
        return {"status": "success", "message": f"Cleaned up audio files older than {max_age_hours} hours"}
    except Exception as e:
        return {"status": "error", "message": str(e)}


# Function to get upload directory path
def get_upload_dir():
    upload_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "uploads", "ipd")
    os.makedirs(upload_dir, exist_ok=True)
    return upload_dir


# Helper function to send WebSocket updates to all connected clients for an upload
async def send_ipd_status_update(upload_id: str, status_data: dict):
    """Send status updates to all WebSocket clients connected for this upload_id"""
    if upload_id in ipd_connections and ipd_connections[upload_id]:
        # Create a copy of the list to avoid modification during iteration
        connections = ipd_connections[upload_id].copy()
        for websocket in connections:
            try:
                await websocket.send_json(status_data)
                print(f"📤 Sent status update to client for upload {upload_id}")
            except Exception as e:
                print(f"⚠️ Error sending update to WebSocket for upload {upload_id}: {str(e)}")
                # Connection might be closed, remove it from the list
                if websocket in ipd_connections[upload_id]:
                    ipd_connections[upload_id].remove(websocket)


async def process_files(upload_id: str):
    """Background task to process uploaded files using OCR and generate discharge summary"""
    try:
        print(f"Starting processing for upload {upload_id}")
        upload_dir = get_upload_dir()
        upload_path = os.path.join(upload_dir, upload_id)
        metadata_path = os.path.join(upload_path, "metadata.json")

        # Read current metadata
        with open(metadata_path, "r") as f:
            metadata = json.load(f)

        # Update status to processing
        print(f"Updating status to processing for upload {upload_id}")
        metadata["processing_status"] = "processing"

        # Save updated metadata
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)
        print(f"Saved processing status for upload {upload_id}")

        # Send WebSocket update
        await send_ipd_status_update(upload_id, {
            "status": "success",
            "processing_status": "processing",
            "upload_id": upload_id,
            "message": "Processing files..."
        })

        # Initialize AWS Textract client
        textract = boto3.client('textract')

        # Process each file with OCR
        documents = {}
        for file_info in metadata["files"]:
            file_path = file_info["saved_path"]
            file_name = file_info["filename"]

            print(f"Processing file: {file_path}")

            try:
                # Read the image file
                with open(file_path, "rb") as document:
                    image_bytes = document.read()

                # Call Textract to extract text
                response = textract.detect_document_text(Document={'Bytes': image_bytes})

                # Compile the extracted text into a single string with spaces
                extracted_text = ' '.join([item["Text"] for item in response["Blocks"] if item["BlockType"] == "LINE"])

                # Add to documents dictionary
                documents[file_name] = extracted_text

                print(f"Successfully extracted text from {file_name}")
            except Exception as e:
                print(f"Error processing file {file_path}: {str(e)}")
                documents[file_name] = f"Error extracting text: {str(e)}"

        # Generate discharge summary from extracted text
        print("Generating discharge summary from extracted text")
        try:
            print(documents)
            discharge_summary = generate_discharge_summary_from_documents(documents)

            # Convert Pydantic model to dictionary for JSON serialization
            discharge_summary_dict = discharge_summary.model_dump()
            print(f"Generated discharge summary with {len(discharge_summary_dict)} fields")
        except Exception as e:
            print(f"Error generating discharge summary: {str(e)}")
            # Create a minimal valid discharge summary with error information
            discharge_summary_dict = {
                "patient_uhid": "Error",
                "patient_name": "Error",
                "admission_number": "Error",
                "admission_datetime": "Error",
                "discharge_status": "Error",
                "treating_consultant": "Error",
                "presenting_complaints": [f"Error generating discharge summary: {str(e)}"],
                "physical_examination": [],
                "general_findings": [],
                "investigations_summary": [],
                "hospital_course": f"Error occurred during processing: {str(e)}",
                "treatment_summary": [],
                "surgery_performed": False,
                "final_diagnosis": "Error",
                "discharge_prescription": [],
                "discharge_advice": ["Please contact support for assistance."]
            }

        # Update status to completed and add discharge summary
        print(f"Updating status to completed for upload {upload_id}")
        metadata["processing_status"] = "completed"
        metadata["discharge_summary"] = discharge_summary_dict

        # Save final metadata
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)
        print(f"Saved completed status for upload {upload_id}")

        # Send WebSocket update for completion
        await send_ipd_status_update(upload_id, {
            "status": "success",
            "processing_status": "completed",
            "upload_id": upload_id,
            "message": "Processing completed successfully",
            "discharge_summary": discharge_summary_dict
        })

    except Exception as e:
        print(f"Error in process_files: {str(e)}")
        # Try to update metadata with error status if possible
        try:
            with open(metadata_path, "r") as f:
                metadata = json.load(f)
            metadata["processing_status"] = "error"
            metadata["error_message"] = str(e)
            with open(metadata_path, "w") as f:
                json.dump(metadata, f, indent=2)

            # Send WebSocket update for error
            await send_ipd_status_update(upload_id, {
                "status": "error",
                "processing_status": "error",
                "upload_id": upload_id,
                "message": f"Error during processing: {str(e)}"
            })
        except Exception as inner_e:
            print(f"Failed to update error status: {str(inner_e)}")


@app.post("/upload_ipd_files")
async def upload_ipd_files(
    background_tasks: BackgroundTasks,
    file0: Optional[UploadFile] = File(None),
    file1: Optional[UploadFile] = File(None),
    file2: Optional[UploadFile] = File(None),
    file3: Optional[UploadFile] = File(None),
    file4: Optional[UploadFile] = File(None),
    file5: Optional[UploadFile] = File(None),
    file6: Optional[UploadFile] = File(None),
    file7: Optional[UploadFile] = File(None),
    file8: Optional[UploadFile] = File(None),
    file9: Optional[UploadFile] = File(None),
    timestamp: Optional[str] = Form(None),
    source: Optional[str] = Form("web_upload")
):
    """Upload multiple files for IPD processing."""
    try:
        # Get upload directory
        upload_dir = get_upload_dir()

        # Collect all uploaded files
        uploaded_files = []
        for i in range(10):
            file_var = locals()[f'file{i}']
            if file_var is not None and file_var.filename:
                uploaded_files.append(file_var)

        # Check if any files were uploaded
        if not uploaded_files:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "No files were uploaded"},
                media_type="application/json"
            )

        # Create a unique directory for this upload
        upload_timestamp = timestamp or datetime.now().isoformat()
        upload_id = f"{int(time.time())}_{upload_timestamp.replace(':', '-')}"
        upload_path = os.path.join(upload_dir, upload_id)
        os.makedirs(upload_path, exist_ok=True)

        # Process each file
        file_responses = []
        for file in uploaded_files:
            # Create a safe filename
            filename = file.filename
            safe_filename = "".join(c if c.isalnum() or c in "._- " else "_" for c in filename)
            file_path = os.path.join(upload_path, safe_filename)

            # Save the file
            content = await file.read()
            with open(file_path, "wb") as f:
                f.write(content)

            # Create response for this file
            file_size = len(content)
            file_responses.append({
                "filename": filename,
                "size": file_size,
                "content_type": file.content_type or "application/octet-stream",
                "saved_path": file_path
            })

        # Create metadata file
        metadata = {
            "upload_id": upload_id,
            "timestamp": upload_timestamp,
            "source": source,
            "processing_status": "uploading",  # Initial status
            "file_count": len(uploaded_files),
            "files": [
                {
                    "filename": resp["filename"],
                    "size": resp["size"],
                    "content_type": resp["content_type"],
                    "saved_path": resp["saved_path"]
                }
                for resp in file_responses
            ]
        }

        # Save metadata to file
        metadata_path = os.path.join(upload_path, "metadata.json")
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)

        # Send initial WebSocket update if any clients are already connected
        # (though this is unlikely at this point)
        if upload_id in ipd_connections and ipd_connections[upload_id]:
            await send_ipd_status_update(upload_id, {
                "status": "success",
                "processing_status": "uploading",
                "upload_id": upload_id,
                "message": f"Successfully uploaded {len(uploaded_files)} files"
            })

        # Start background processing
        background_tasks.add_task(process_files, upload_id)

        # Return success response
        return JSONResponse(
            content={
                "status": "success",
                "message": f"Successfully uploaded {len(uploaded_files)} files",
                "upload_id": upload_id,
                "files": [
                    {
                        "filename": resp["filename"],
                        "size": resp["size"],
                        "content_type": resp["content_type"]
                    }
                    for resp in file_responses
                ]
            },
            media_type="application/json"
        )

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)},
            media_type="application/json"
        )


@app.get("/ipd_upload/{upload_id}")
async def get_ipd_upload(upload_id: str):
    """Get details of a specific IPD upload."""
    try:
        # Get upload directory
        upload_dir = get_upload_dir()

        # Read from file
        upload_path = os.path.join(upload_dir, upload_id)
        if not os.path.isdir(upload_path):
            return JSONResponse(
                status_code=404,
                content={"status": "error", "message": f"Upload {upload_id} not found"},
                media_type="application/json"
            )

        metadata_path = os.path.join(upload_path, "metadata.json")
        if not os.path.exists(metadata_path):
            return JSONResponse(
                status_code=404,
                content={"status": "error", "message": f"Metadata for upload {upload_id} not found"},
                media_type="application/json"
            )

        with open(metadata_path, "r") as f:
            metadata = json.load(f)

        return JSONResponse(
            content={
                "status": "success",
                "upload": metadata
            },
            media_type="application/json"
        )

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)},
            media_type="application/json"
        )


@app.get("/ipd_uploads")
async def list_ipd_uploads():
    """List all IPD uploads."""
    try:
        # Get upload directory
        upload_dir = get_upload_dir()

        uploads = []
        for upload_id in os.listdir(upload_dir):
            upload_path = os.path.join(upload_dir, upload_id)
            if os.path.isdir(upload_path):
                metadata_path = os.path.join(upload_path, "metadata.json")
                if os.path.exists(metadata_path):
                    with open(metadata_path, "r") as f:
                        metadata = json.load(f)
                        uploads.append(metadata)

        return JSONResponse(
            content={
                "status": "success",
                "uploads": uploads
            },
            media_type="application/json"
        )

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)},
            media_type="application/json"
        )


@app.websocket("/ws/ipd/{upload_id}")
async def ipd_websocket_endpoint(websocket: WebSocket, upload_id: str):
    """WebSocket endpoint for IPD status updates."""
    await websocket.accept()
    print(f"🔗 Client connected to IPD WebSocket for upload {upload_id}")

    try:
        # Add this connection to the list for this upload_id
        if upload_id not in ipd_connections:
            ipd_connections[upload_id] = []
        ipd_connections[upload_id].append(websocket)

        # Send initial status update
        try:
            # Get current status from metadata file
            upload_dir = get_upload_dir()
            upload_path = os.path.join(upload_dir, upload_id)
            metadata_path = os.path.join(upload_path, "metadata.json")

            if os.path.exists(metadata_path):
                with open(metadata_path, "r") as f:
                    metadata = json.load(f)

                # Send current status
                await websocket.send_json({
                    "status": "success",
                    "upload_id": upload_id,
                    "processing_status": metadata.get("processing_status", "unknown"),
                    "message": f"Current status: {metadata.get('processing_status', 'unknown')}"
                })
            else:
                await websocket.send_json({
                    "status": "error",
                    "message": f"Upload {upload_id} not found"
                })
        except Exception as e:
            print(f"⚠️ Error sending initial status: {str(e)}")
            await websocket.send_json({
                "status": "error",
                "message": f"Error retrieving status: {str(e)}"
            })

        # Keep connection alive until client disconnects
        while True:
            # Wait for any message from client (ping/pong or close)
            data = await websocket.receive_text()
            print(f"📥 Received from IPD WebSocket client: {data}")

            # You could handle specific commands here if needed
            if data.lower() == "ping":
                await websocket.send_text("pong")

    except WebSocketDisconnect:
        print(f"❌ Client disconnected from IPD WebSocket for upload {upload_id}")
    except Exception as e:
        print(f"⚠️ Error in IPD WebSocket: {str(e)}")
    finally:
        # Remove this connection from the list
        if upload_id in ipd_connections and websocket in ipd_connections[upload_id]:
            ipd_connections[upload_id].remove(websocket)
            print(f"🗑️ Removed client from IPD WebSocket connections for upload {upload_id}")

        # Clean up empty lists
        if upload_id in ipd_connections and not ipd_connections[upload_id]:
            del ipd_connections[upload_id]
            print(f"🗑️ Removed empty connection list for upload {upload_id}")




# Background task to clean up old audio files periodically
@app.on_event("startup")
async def start_cleanup_scheduler():
    async def cleanup_scheduler():
        while True:
            try:
                # Run cleanup every 6 hours
                cleanup_old_audio_files(max_age_hours=24)
                # Sleep for 6 hours
                await asyncio.sleep(6 * 3600)
            except Exception as e:
                logger.error(f"[Scheduler] Error in cleanup scheduler: {str(e)}")
                # If there's an error, wait a bit and try again
                await asyncio.sleep(300)

    # Start the cleanup scheduler as a background task
    asyncio.create_task(cleanup_scheduler())
    logger.info("[Startup] Started audio file cleanup scheduler")

@app.on_event("startup")
async def log_app_startup():
    """Log when the FastAPI application starts"""
    logger.info("=== DoQ FastAPI Application Started ===")
    logger.info(f"Server running at http://0.0.0.0:8000")
    logger.info(f"Log files are being written to logs/doq.log")


def start():
    # Initialize logging and redirect stdout/stderr to the logger
    logger.info("Starting DoQ server")
    setup_logging()

    # Log startup message
    logger.info("All terminal output will now be written to logs/doq.log")

    # Start the uvicorn server
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

if __name__ == "__main__":
    start()


















# @app.post('/differentials')
# async def receive_data(data: Template):
#     print("Received data for Differentials ----------\n", str(data.chatHistory))
#     response = Differentials_agent(str(data.chatHistory))
#     print('Pushed data for Differentials ----------\n', response)

#     return {
#         "differentials": response,
#     }


# @app.post("/patient_chat")
# async def receive_data(data: Template):
#     print("Received data for PatChat ----------\n")
#     print(data.chatHistory, data.patientData)
#     response = PatChat_agent(data.chatHistory, data.patientData)
#     print('Pushed data for PatChat ----------\n', response)
#     return {
#         "response": response,
#     }


# # Helper functions for media processing
# def encode_media_file(media_url, auth_token, account_sid):
#     """Download and encode a media file (PDF or image)"""
#     try:
#         # Download the media file
#         response = requests.get(
#             media_url,
#             auth=(account_sid, auth_token),
#             timeout=30,
#             headers={'User-Agent': 'Mozilla/5.0'}
#         )

#         if response.status_code != 200:
#             raise Exception(f"Failed to download media: {response.status_code}")

#         content_type = response.headers.get('Content-Type', '')
        
#         # Create a temporary file to store the downloaded content
#         with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(media_url)[1]) as temp_file:
#             temp_file.write(response.content)
#             temp_path = temp_file.name

#         # Process based on content type
#         if "pdf" in content_type:
#             # Convert PDF to images
#             images = convert_from_path(temp_path)
#             encoded_images = []
#             for img in images:
#                 buffered = BytesIO()
#                 img.save(buffered, format="JPEG")
#                 encoded_images.append(base64.b64encode(buffered.getvalue()).decode('utf-8'))
#             return {
#                 "type": "pdf",
#                 "content_type": "image/jpeg",
#                 "encoded_images": encoded_images
#             }
#         else:
#             # Handle image files
#             with open(temp_path, "rb") as image_file:
#                 encoded_image = base64.b64encode(image_file.read()).decode('utf-8')
#             return {
#                 "type": "image",
#                 "content_type": content_type,
#                 "encoded_images": [encoded_image]
#             }

#     except Exception as e:
#         raise Exception(f"Error encoding media: {str(e)}")
#     finally:
#         # Clean up temporary file
#         if 'temp_path' in locals():
#             os.unlink(temp_path)

# def process_lab_reports(encoded_reports):
#     """Process all encoded lab reports in one go"""
#     try:
#         # Prepare messages for OpenAI
#         messages = []
        
#         # Add system prompt
#         messages.append({
#             "role": "system",
#             "content": "You are a medical assistant helping doctors analyze patient lab reports. Your task is to extract text from medical lab reports and identify clinically relevant information. This is for legitimate medical purposes to help patients and doctors. First extract all visible text from the images, then identify key medical information, abnormal values, and clinically significant findings. Format your response in two parts: 1) EXTRACTED TEXT: containing all visible text from the images, and 2) ANALYSIS: containing a concise, bulleted list of the most important findings."
#         })

#         # Add user message with all encoded images
#         user_content = [
#             {"type": "text", "text": "This is a collection of medical lab reports from a patient. Please extract all text from these lab reports and analyze the important medical information to help the doctor prepare for consultation:"}
#         ]

#         # Add all encoded images
#         for report in encoded_reports:
#             for encoded_image in report["encoded_images"]:
#                 user_content.append({
#                     "type": "image_url",
#                     "image_url": {
#                         "url": f"data:{report['content_type']};base64,{encoded_image}",
#                         "detail": "high"
#                     }
#                 })

#         messages.append({
#             "role": "user",
#             "content": user_content
#         })

#         # Call OpenAI API
#         response = client.chat.completions.create(
#             model="gpt-4o",
#             messages=messages,
#             temperature=0.3,
#             max_tokens=4000
#         )

#         return response.choices[0].message.content

#     except Exception as e:
#         raise Exception(f"Error processing lab reports: {str(e)}")

# def handle_lab_reports(session, message_body, media_url, from_number, auth_token, account_sid, r, redis_key):
#     """Handle lab report processing state"""
#     language = session.get('language', 'en')

#     # Check if user typed 'exit' to end lab report sharing
#     if message_body.lower() == "exit":
#         # Process collected lab reports if any
#         if 'encoded_reports' in session and session['encoded_reports']:
#             try:
#                 # Process all reports in one go
#                 lab_report_analysis = process_lab_reports(session['encoded_reports'])

#                 # Get the existing report
#                 report_key = session['report_key']
#                 doc_id = session['doc_id']
#                 patient_id = session['patient_id']

#                 # Update the report with lab report information
#                 update_data = {
#                     "lab_reports": lab_report_analysis
#                 }

#                 # Save the updated report
#                 save_new_patient_report(doc_id, patient_id, report_key, update_data)

#                 # Send confirmation message
#                 if language == "hi":
#                     completion_message = "आपकी लैब रिपोर्ट्स को सफलतापूर्वक प्रोसेस किया गया है और आपकी प्री-कंसल्टेशन रिपोर्ट में जोड़ा गया है। धन्यवाद!"
#                 else:
#                     completion_message = "Your lab reports have been successfully processed and added to your pre-consultation report. Thank you!"

#                 send_message(from_number, completion_message, language)

#             except Exception as e:
#                 print(f"Error processing lab reports: {e}")
#                 # Send error message
#                 if language == "hi":
#                     error_message = "आपकी लैब रिपोर्ट्स को प्रोसेस करने में एक त्रुटि हुई। कृपया अपने डॉक्टर से संपर्क करें।"
#                 else:
#                     error_message = "There was an error processing your lab reports. Please contact your doctor."

#                 send_message(from_number, error_message, language)
#         else:
#             # No lab reports were shared
#             if language == "hi":
#                 no_reports_message = "कोई लैब रिपोर्ट साझा नहीं की गई। धन्यवाद!"
#             else:
#                 no_reports_message = "No lab reports were shared. Thank you!"

#             send_message(from_number, no_reports_message, language)

#         # End the session
#         r.delete(redis_key)
#         return

#     # Handle media uploads (lab reports)
#     if media_url:
#         try:
#             # Encode the media file
#             encoded_report = encode_media_file(media_url, auth_token, account_sid)

#             # Initialize encoded_reports list if it doesn't exist
#             if 'encoded_reports' not in session:
#                 session['encoded_reports'] = []

#             # Add the encoded report to the session
#             session['encoded_reports'].append(encoded_report)

#             # Send confirmation message
#             if language == "hi":
#                 confirmation_message = "लैब रिपोर्ट प्राप्त हुई और प्रोसेस की गई। यदि आपके पास अधिक रिपोर्ट हैं, तो कृपया उन्हें भेजें, अन्यथा 'exit' टाइप करें।"
#             else:
#                 confirmation_message = "Lab report received and processed. If you have more reports, please send them, otherwise type 'exit'."

#             send_message(from_number, confirmation_message, language)

#             # Update the session
#             r.set(redis_key, json.dumps(session), ex=1800)

#         except Exception as e:
#             print(f"Error processing lab report: {e}")
#             # Send error message
#             if language == "hi":
#                 error_message = "आपकी लैब रिपोर्ट को प्रोसेस करने में एक त्रुटि हुई। कृपया पुनः प्रयास करें या 'exit' टाइप करें।"
#             else:
#                 error_message = "There was an error processing your lab report. Please try again or type 'exit'."

#             send_message(from_number, error_message, language)
#     else:
#         # Remind the user to send a PDF/image or type 'exit'
#         if language == "hi":
#             reminder_message = "कृपया एक पीडीएफ या इमेज भेजें, या जब आप पूरा कर लें तो 'exit' टाइप करें।"
#         else:
#             reminder_message = "Please send a PDF or image, or type 'exit' when you're done."

#         send_message(from_number, reminder_message, language)



